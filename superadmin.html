<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OUTA - Super Admin Panel</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Outfit:wght@300;400;500;700;900&family=Source+Code+Pro:wght@400;700&display=swap" rel="stylesheet">
    <script src="https://unpkg.com/lucide@latest"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.2/dist/chart.umd.min.js"></script>
    <style>
        :root { --sidebar-width: 280px; }
        body {
            font-family: 'Outfit', sans-serif;
            background-color: #0a0a1a;
            color: #e0e0e0;
        }
        .sidebar { width: var(--sidebar-width); transition: transform 0.3s ease-in-out; }
        @media (max-width: 1024px) {
            .sidebar { transform: translateX(-100%); position: fixed; z-index: 50; height: 100%; }
            .sidebar.open { transform: translateX(0); }
            main { padding-left: 0; }
        }
        main { transition: padding-left 0.3s ease-in-out; padding-left: var(--sidebar-width); }
        .content-card {
            background: rgba(26, 26, 78, 0.4);
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }
        .sidebar-link:hover, .sidebar-link.active {
            background-color: rgba(220, 38, 38, 0.2);
            color: #fff;
            border-right: 3px solid #dc2626;
        }
         .sidebar-link.active i, .sidebar-link:hover i { color: #f87171; }
        .modal { transition: opacity 0.3s ease, transform 0.3s ease; }
        .modal:not(.open) { opacity: 0; transform: scale(0.95); pointer-events: none; }
        .modal.open { opacity: 1; transform: scale(1); pointer-events: auto; }
        .code-editor {
            font-family: 'Source Code Pro', monospace;
            background-color: #1a1a2e;
            border: 1px solid #2a2a4a;
        }
        ::-webkit-scrollbar { width: 8px; }
        ::-webkit-scrollbar-track { background: #111122; }
        ::-webkit-scrollbar-thumb { background: #4b5563; border-radius: 4px; }
        ::-webkit-scrollbar-thumb:hover { background: #dc2626; }
    </style>
</head>
<body class="antialiased">
    <div id="sidebar-overlay" class="fixed inset-0 bg-black/60 z-40 hidden lg:hidden"></div>
    <div class="flex min-h-screen">
        <!-- Sidebar -->
        <aside id="sidebar" class="sidebar bg-[#111122] flex flex-col fixed top-0 left-0 h-full overflow-y-auto z-50">
            <div class="p-6 flex items-center gap-2 border-b border-red-500/20">
                <i data-lucide="shield-alert" class="w-8 h-8 text-red-500"></i>
                <h1 class="text-xl font-black tracking-tighter text-white">SUPER ADMIN</h1>
            </div>
            <nav class="flex-1 px-4 py-4 space-y-2">
                <a href="#" class="sidebar-link active flex items-center gap-3 p-3 rounded-md text-gray-300" data-page="page-dashboard">
                    <i data-lucide="layout-dashboard" class="w-5 h-5"></i> Dashboard
                </a>
                <a href="#" class="sidebar-link flex items-center gap-3 p-3 rounded-md text-gray-300" data-page="page-users">
                    <i data-lucide="users" class="w-5 h-5"></i> User Management
                </a>
            </nav>
            <div class="p-4 mt-auto border-t border-gray-700">
                 <a href="home.html" class="sidebar-link flex items-center gap-3 p-3 rounded-md text-gray-300">
                    <i data-lucide="log-out" class="w-5 h-5"></i> Exit Admin Panel
                </a>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="flex-1 w-full">
            <header class="bg-[#111122]/80 backdrop-blur-sm sticky top-0 z-30 flex items-center justify-between p-4 border-b border-gray-800">
                <div class="flex items-center gap-4">
                    <button id="menu-btn" class="lg:hidden p-2 rounded-md hover:bg-gray-700">
                        <i data-lucide="menu" class="w-6 h-6"></i>
                    </button>
                    <h2 id="page-title" class="text-xl font-bold">Dashboard</h2>
                </div>
            </header>
            
            <div class="p-4 sm:p-6 md:p-8">
                <!-- Dashboard -->
                <div id="page-dashboard" class="page-content">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6 sm:mb-8">
                        <div class="content-card p-5 rounded-lg"><p class="text-sm font-medium text-gray-400">Total Users</p><p class="text-2xl sm:text-3xl font-bold mt-2">1,284</p></div>
                        <div class="content-card p-5 rounded-lg"><p class="text-sm font-medium text-gray-400">Active Sessions</p><p class="text-2xl sm:text-3xl font-bold mt-2">312</p></div>
                        <div class="content-card p-5 rounded-lg"><p class="text-sm font-medium text-gray-400">Total Pages Hosted</p><p class="text-2xl sm:text-3xl font-bold mt-2">845</p></div>
                        <div class="content-card p-5 rounded-lg"><p class="text-sm font-medium text-gray-400">Blocked Users</p><p class="text-2xl sm:text-3xl font-bold mt-2 text-red-500">12</p></div>
                    </div>
                     <div class="content-card p-5 rounded-lg">
                        <h3 class="font-bold mb-4">User Registrations (Last 30 Days)</h3>
                        <div class="relative h-80"><canvas id="user-regs-chart"></canvas></div>
                    </div>
                </div>

                <!-- User Management -->
                <div id="page-users" class="page-content hidden">
                    <div id="user-management-table" class="content-card rounded-lg p-5"></div>
                </div>

            </div>
        </main>
    </div>
    
    <!-- Block User Modal -->
    <div id="block-modal" class="modal fixed inset-0 z-50 flex items-center justify-center p-4">
        <div class="modal-backdrop fixed inset-0 bg-black/70 backdrop-blur-sm"></div>
        <div class="content-card rounded-xl w-full max-w-md relative">
            <div class="p-6 text-center">
                <i data-lucide="shield-off" class="w-16 h-16 mx-auto text-red-500"></i>
                <h3 class="mt-4 text-xl font-bold">Block User?</h3>
                <p class="mt-2 text-sm text-gray-400">Are you sure you want to block <b id="user-to-block-name"></b>? They will lose access to their account and hosted pages.</p>
            </div>
            <div class="p-4 bg-gray-700/20 flex justify-end gap-4 rounded-b-xl">
                <button id="cancel-block-btn" class="py-2 px-5 rounded-lg font-semibold hover:bg-gray-600">Cancel</button>
                <button id="confirm-block-btn" class="py-2 px-5 rounded-lg font-semibold bg-red-600 hover:bg-red-500 text-white">Block User</button>
            </div>
        </div>
    </div>

    <script>
        // --- FAKE DATA & STATE ---
        const state = {
            users: [
                { id: 'usr_1a2b', email: '<EMAIL>', regDate: '2025-08-25', status: 'Active' },
                { id: 'usr_3c4d', email: '<EMAIL>', regDate: '2025-08-24', status: 'Active' },
                { id: 'usr_5e6f', email: '<EMAIL>', regDate: '2025-08-22', status: 'Active' },
                { id: 'usr_7g8h', email: '<EMAIL>', regDate: '2025-08-21', status: 'Blocked' },
            ]
        };
        let userIdToBlock = null;

        // --- UI ELEMENTS & HELPERS ---
        const getElement = id => document.getElementById(id);
        const queryAll = selector => document.querySelectorAll(selector);

        // --- CHARTING ---
        const chartInstances = {};
        const renderChart = (ctxId, type, labels, data, label, color) => {
            const ctx = getElement(ctxId)?.getContext('2d');
            if (!ctx) return;
            if (chartInstances[ctxId]) chartInstances[ctxId].destroy();
            chartInstances[ctxId] = new Chart(ctx, {
                type,
                data: { labels, datasets: [{ label, data, borderColor: color, backgroundColor: `${color}33`, fill: true, tension: 0.4 }] },
                options: { responsive: true, maintainAspectRatio: false, plugins: { legend: { labels: { color: '#e0e0e0' } } }, scales: { x: { ticks: { color: '#9ca3af' }, grid: { color: 'rgba(255, 255, 255, 0.1)' } }, y: { ticks: { color: '#9ca3af' }, grid: { color: 'rgba(255, 255, 255, 0.1)' } } } }
            });
        };

        // --- NAVIGATION ---
        const sidebar = getElement('sidebar');
        const sidebarOverlay = getElement('sidebar-overlay');
        const sidebarLinks = queryAll('.sidebar-link');
        const pageContents = queryAll('.page-content');
        const pageTitle = getElement('page-title');

        const closeSidebar = () => {
            sidebar.classList.remove('open');
            sidebarOverlay.classList.add('hidden');
        };

        const openSidebar = () => {
            sidebar.classList.add('open');
            sidebarOverlay.classList.remove('hidden');
        };

        const navigateToPage = pageId => {
            sidebarLinks.forEach(l => l.classList.toggle('active', l.dataset.page === pageId));
            pageContents.forEach(page => page.classList.toggle('hidden', page.id !== pageId));
            pageTitle.textContent = document.querySelector(`.sidebar-link[data-page="${pageId}"]`).textContent.trim();
            if (window.innerWidth < 1024) closeSidebar();
            // Lazy load charts/content
            setTimeout(() => {
                if (pageId === 'page-dashboard') renderChart('user-regs-chart', 'line', ['W1', 'W2', 'W3', 'W4'], [120, 180, 150, 250], 'New Users', '#f87171');
            }, 0);
        };
        sidebarLinks.forEach(link => {
            if (link.dataset.page) {
                link.addEventListener('click', e => { e.preventDefault(); navigateToPage(link.dataset.page); });
            }
        });
        getElement('menu-btn').addEventListener('click', (e) => {
            e.stopPropagation();
            sidebar.classList.contains('open') ? closeSidebar() : openSidebar();
        });
        sidebarOverlay.addEventListener('click', closeSidebar);


        // --- RENDER FUNCTIONS ---
        const getStatusBadge = status => {
            const isBlocked = status === 'Blocked';
            return `<span class="px-2.5 py-1 text-xs font-medium rounded-full ${isBlocked ? 'bg-red-500/20 text-red-400' : 'bg-emerald-500/20 text-emerald-400'}">${status}</span>`;
        };

        const renderUserTable = () => {
            let tableHTML = `<h3 class="font-bold text-lg mb-4">User Management</h3><div class="overflow-x-auto"><table class="w-full text-sm text-left"><thead class="border-b border-gray-700 text-xs text-gray-400 uppercase"><tr><th class="px-6 py-3">User ID</th><th class="px-6 py-3">Email</th><th class="px-6 py-3">Registration Date</th><th class="px-6 py-3">Status</th><th class="px-6 py-3 text-right">Actions</th></tr></thead><tbody>`;
            state.users.forEach(user => {
                tableHTML += `<tr class="border-b border-gray-800 hover:bg-gray-700/20" data-user-id="${user.id}">
                    <td class="px-6 py-4 font-mono text-gray-400">${user.id}</td>
                    <td class="px-6 py-4">${user.email}</td>
                    <td class="px-6 py-4">${user.regDate}</td>
                    <td class="px-6 py-4 status-cell">${getStatusBadge(user.status)}</td>
                    <td class="px-6 py-4 text-right">
                        ${user.status !== 'Blocked' ? `<button title="Block User" class="block-user-btn p-2 hover:bg-red-500/20 rounded-md"><i data-lucide="shield-off" class="w-4 h-4 text-red-400"></i></button>` : ''}
                    </td>
                </tr>`;
            });
            tableHTML += `</tbody></table></div>`;
            getElement('user-management-table').innerHTML = tableHTML;
            lucide.createIcons();
        };

        // --- MODAL & ACTIONS ---
        const blockModal = getElement('block-modal');
        const openModal = (modal) => modal.classList.add('open');
        const closeModal = (modal) => modal.classList.remove('open');

        getElement('user-management-table').addEventListener('click', e => {
            const blockBtn = e.target.closest('.block-user-btn');
            if (blockBtn) {
                userIdToBlock = blockBtn.closest('tr').dataset.userId;
                const user = state.users.find(u => u.id === userIdToBlock);
                getElement('user-to-block-name').textContent = user.email;
                openModal(blockModal);
            }
        });

        getElement('cancel-block-btn').addEventListener('click', () => closeModal(blockModal));
        getElement('confirm-block-btn').addEventListener('click', () => {
            const user = state.users.find(u => u.id === userIdToBlock);
            if (user) user.status = 'Blocked';
            renderUserTable();
            closeModal(blockModal);
        });
        
        // --- INITIAL LOAD ---
        document.addEventListener('DOMContentLoaded', () => {
            lucide.createIcons();
            renderUserTable();
            navigateToPage('page-dashboard');
        });
    </script>
</body>
</html>

