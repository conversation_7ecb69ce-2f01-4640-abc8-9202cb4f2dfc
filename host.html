<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OUTA - Admin Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Outfit:wght@300;400;500;700;900&family=Source+Code+Pro:wght@400;700&display=swap" rel="stylesheet">
    <script src="https://unpkg.com/lucide@latest"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.2/dist/chart.umd.min.js"></script>
    <style>
        :root {
            --sidebar-width: 280px;
        }
        body {
            font-family: 'Outfit', sans-serif;
            background-color: #0a0a1a;
            color: #e0e0e0;
        }
        .sidebar {
            width: var(--sidebar-width);
            transition: transform 0.3s ease-in-out;
        }
        @media (max-width: 1024px) {
            .sidebar {
                transform: translateX(-100%);
                position: fixed;
                z-index: 50;
                height: 100%;
            }
            .sidebar.open {
                transform: translateX(0);
            }
            main {
                padding-left: 0;
            }
        }
        main {
            transition: padding-left 0.3s ease-in-out;
            padding-left: var(--sidebar-width);
        }
        .kpi-card, .content-card {
            background: rgba(26, 26, 78, 0.4);
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }
        .dropdown-panel {
            background-color: #1e1b34;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .sidebar-link {
            transition: all 0.2s ease;
        }
        .sidebar-link:hover, .sidebar-link.active {
            background-color: rgba(99, 102, 241, 0.2);
            color: #fff;
            border-right: 3px solid #6366f1;
        }
        .sidebar-link.active i, .sidebar-link:hover i {
            color: #818cf8;
        }
        .modal-backdrop {
            background-color: rgba(0,0,0,0.7);
            backdrop-filter: blur(5px);
        }
        .modal {
            transition: opacity 0.3s ease, transform 0.3s ease;
        }
        .modal:not(.open) {
            opacity: 0;
            transform: scale(0.95);
            pointer-events: none;
        }
        .modal.open {
            opacity: 1;
            transform: scale(1);
            pointer-events: auto;
        }
        .code-editor {
            font-family: 'Source Code Pro', monospace;
            background-color: #1a1a2e;
            border: 1px solid #2a2a4a;
        }
        ::-webkit-scrollbar { width: 8px; height: 8px; }
        ::-webkit-scrollbar-track { background: #111122; }
        ::-webkit-scrollbar-thumb { background: #2a2a4a; border-radius: 4px; }
        ::-webkit-scrollbar-thumb:hover { background: #6366f1; }
    </style>
</head>
<body class="antialiased">

    <div id="sidebar-overlay" class="fixed inset-0 bg-black/60 z-40 hidden lg:hidden"></div>
    <div class="flex min-h-screen">
        <!-- Sidebar -->
        <aside id="sidebar" class="sidebar bg-[#111122] flex flex-col fixed top-0 left-0 h-full overflow-y-auto z-50">
            <div class="p-6 flex items-center gap-2">
                <h1 class="text-3xl font-black tracking-tighter text-white">OUTA</h1>
                <span class="bg-yellow-400 text-black text-xs font-bold px-2 py-1 rounded-full -translate-y-2">Admin</span>
            </div>
            <nav class="flex-1 px-4 space-y-2">
                <a href="#" class="sidebar-link active flex items-center gap-3 p-3 rounded-md text-gray-300" data-page="page-dashboard">
                    <i data-lucide="layout-dashboard" class="w-5 h-5"></i> Dashboard
                </a>
                <a href="#" class="sidebar-link flex items-center gap-3 p-3 rounded-md text-gray-300" data-page="page-hosted-pages">
                    <i data-lucide="globe-2" class="w-5 h-5"></i> Hosted Pages
                </a>
                <a href="#" class="sidebar-link flex items-center gap-3 p-3 rounded-md text-gray-300" data-page="page-analytics">
                    <i data-lucide="bar-chart-3" class="w-5 h-5"></i> Analytics
                </a>
                <a href="#" class="sidebar-link flex items-center gap-3 p-3 rounded-md text-gray-300" data-page="page-orders">
                    <i data-lucide="shopping-cart" class="w-5 h-5"></i> Orders
                </a>
                <a href="#" class="sidebar-link flex items-center gap-3 p-3 rounded-md text-gray-300" data-page="page-products">
                    <i data-lucide="package" class="w-5 h-5"></i> Products
                </a>
                <a href="#" class="sidebar-link flex items-center gap-3 p-3 rounded-md text-gray-300" data-page="page-customers">
                    <i data-lucide="users" class="w-5 h-5"></i> Customers
                </a>
                <a href="#" class="sidebar-link flex items-center gap-3 p-3 rounded-md text-gray-300" data-page="page-marketing">
                    <i data-lucide="megaphone" class="w-5 h-5"></i> Marketing
                </a>
            </nav>
            <div class="p-4 mt-auto border-t border-gray-700">
                 <a href="home.html" class="sidebar-link flex items-center gap-3 p-3 rounded-md text-gray-300">
                    <i data-lucide="arrow-left-circle" class="w-5 h-5"></i> Back to Home
                </a>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="flex-1 w-full">
             <!-- Header -->
            <header class="bg-[#111122]/80 backdrop-blur-sm sticky top-0 z-30 flex items-center justify-between p-4 border-b border-gray-800">
                <div class="flex items-center gap-4">
                    <button id="menu-btn" class="lg:hidden p-2 rounded-md hover:bg-gray-700">
                        <i data-lucide="menu" class="w-6 h-6"></i>
                    </button>
                    <h2 id="page-title" class="text-xl font-bold">Dashboard</h2>
                </div>
                <div class="flex items-center gap-2 sm:gap-4">
                     <div class="hidden sm:block text-right">
                        <p id="current-time" class="text-sm font-semibold"></p>
                        <p id="current-date" class="text-xs text-gray-400"></p>
                    </div>
                    <!-- Notification Dropdown -->
                    <div class="relative">
                        <button id="notification-btn" class="p-2 rounded-full hover:bg-gray-700 relative">
                            <i data-lucide="bell" class="w-5 h-5"></i>
                            <span id="notification-dot" class="absolute top-1 right-1 block h-2 w-2 rounded-full bg-red-500 ring-2 ring-[#111122]"></span>
                        </button>
                        <div id="notification-dropdown" class="absolute right-0 mt-2 w-80 dropdown-panel rounded-md shadow-lg z-50 hidden">
                            <div class="p-3 font-bold border-b border-gray-700">Notifications</div>
                            <div class="py-2 max-h-80 overflow-y-auto">
                                <a href="#" class="flex items-start gap-3 px-4 py-3 text-sm text-gray-300 hover:bg-indigo-600/20 transition-colors">
                                    <div class="bg-emerald-500/20 p-2 rounded-full"><i data-lucide="shopping-cart" class="w-4 h-4 text-emerald-400"></i></div>
                                    <div>
                                        <p><strong>New Order #9C1A0F</strong> received from Ken Tanaka.</p>
                                        <p class="text-xs text-gray-500 mt-1">5 minutes ago</p>
                                    </div>
                                </a>
                                <a href="#" class="flex items-start gap-3 px-4 py-3 text-sm text-gray-300 hover:bg-indigo-600/20 transition-colors">
                                    <div class="bg-blue-500/20 p-2 rounded-full"><i data-lucide="user-plus" class="w-4 h-4 text-blue-400"></i></div>
                                    <div>
                                        <p><strong>New User Signup:</strong> <EMAIL></p>
                                        <p class="text-xs text-gray-500 mt-1">1 hour ago</p>
                                    </div>
                                </a>
                                <a href="#" class="flex items-start gap-3 px-4 py-3 text-sm text-gray-300 hover:bg-indigo-600/20 transition-colors">
                                   <div class="bg-yellow-500/20 p-2 rounded-full"><i data-lucide="alert-triangle" class="w-4 h-4 text-yellow-400"></i></div>
                                    <div>
                                        <p>Page <strong>"Cyber Watch"</strong> will expire in 15 days.</p>
                                        <p class="text-xs text-gray-500 mt-1">2 hours ago</p>
                                    </div>
                                </a>
                            </div>
                            <div class="p-2 text-center border-t border-gray-700">
                                <a href="#" id="view-all-notifications-btn" class="text-xs text-indigo-400 hover:underline">View All Notifications</a>
                            </div>
                        </div>
                    </div>
                    <!-- User Dropdown -->
                    <div class="relative">
                        <button id="user-menu-btn" class="block">
                            <img src="https://placehold.co/40x40/6366f1/ffffff?text=U" alt="User Avatar" class="w-9 h-9 rounded-full">
                        </button>
                        <div id="user-menu-dropdown" class="absolute right-0 mt-2 w-48 dropdown-panel rounded-md shadow-lg py-1 z-50 hidden">
                            <a href="#" class="flex items-center gap-3 px-4 py-2 text-sm text-gray-300 hover:bg-indigo-600/50 transition-colors">
                                <i data-lucide="log-out" class="w-4 h-4"></i> Sign Out
                            </a>
                        </div>
                    </div>
                </div>
            </header>
            
            <div class="p-4 sm:p-6 md:p-8">
                <div id="page-dashboard" class="page-content">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6 sm:mb-8">
                        <div class="kpi-card p-5 rounded-lg"><p class="text-sm font-medium text-gray-400">Total Visitors</p><p class="text-2xl sm:text-3xl font-bold mt-2">12,483</p><p class="text-xs text-emerald-400 mt-1">+12.5% this month</p></div>
                        <div class="kpi-card p-5 rounded-lg"><p class="text-sm font-medium text-gray-400">Total Revenue</p><p class="text-2xl sm:text-3xl font-bold mt-2">$8,210</p><p class="text-xs text-emerald-400 mt-1">+8.2% this month</p></div>
                        <div class="kpi-card p-5 rounded-lg"><p class="text-sm font-medium text-gray-400">Conversion Rate</p><p class="text-2xl sm:text-3xl font-bold mt-2">3.4%</p><p class="text-xs text-red-400 mt-1">-1.1% this month</p></div>
                        <div class="kpi-card p-5 rounded-lg"><p class="text-sm font-medium text-gray-400">New Orders</p><p class="text-2xl sm:text-3xl font-bold mt-2">142</p><p class="text-xs text-emerald-400 mt-1">+22 since yesterday</p></div>
                    </div>
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
                        <div class="lg:col-span-2 content-card p-5 rounded-lg">
                            <h3 class="font-bold mb-4">Sales Analytics</h3>
                            <div class="relative h-72"><canvas id="sales-chart"></canvas></div>
                        </div>
                        <div class="content-card p-5 rounded-lg">
                            <h3 class="font-bold mb-4">Traffic Sources</h3>
                            <div class="relative h-72"><canvas id="traffic-chart"></canvas></div>
                        </div>
                    </div>
                    <div id="recent-orders-container" class="content-card rounded-lg"></div>
                </div>

                <div id="page-hosted-pages" class="page-content hidden">
                    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
                        <h3 class="font-bold text-2xl">Your Hosted Pages</h3>
                        <button id="host-new-page-btn" class="bg-indigo-600 hover:bg-indigo-500 text-white font-bold py-2 px-4 rounded-lg flex items-center gap-2 w-full sm:w-auto">
                            <i data-lucide="plus" class="w-5 h-5"></i> Host New Page
                        </button>
                    </div>
                    <div class="content-card rounded-lg">
                        <div id="pages-list-container" class="overflow-x-auto"></div>
                    </div>
                </div>

                <div id="page-analytics" class="page-content hidden">
                    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
                        <div>
                             <h3 class="font-bold text-2xl">Page Analytics</h3>
                             <p id="analytics-page-title" class="text-sm text-gray-400">Showing data for all pages</p>
                        </div>
                        <select id="analytics-page-selector" class="bg-[#111122] border border-gray-700 rounded-md p-2 w-full sm:w-auto">
                            <option value="all">All Pages</option>
                        </select>
                    </div>
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div class="content-card p-5 rounded-lg">
                            <h3 class="font-bold mb-4">Visitors by Country</h3>
                            <div class="relative h-72"><canvas id="country-chart"></canvas></div>
                        </div>
                        <div class="content-card p-5 rounded-lg">
                            <h3 class="font-bold mb-4">Visitors by Device</h3>
                            <div class="relative h-72"><canvas id="device-chart"></canvas></div>
                        </div>
                    </div>
                </div>

                <div id="page-orders" class="page-content hidden"><div id="all-orders-container" class="content-card rounded-lg p-5"></div></div>
                <div id="page-products" class="page-content hidden"><h3 class="font-bold text-2xl mb-6">Your Products</h3><div id="products-list-container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"></div></div>
                <div id="page-customers" class="page-content hidden"><div id="all-customers-container" class="content-card rounded-lg p-5"></div></div>
                <div id="page-marketing" class="page-content hidden">
                    <div class="content-card rounded-lg p-5">
                        <h3 class="font-bold text-lg mb-4">Marketing & Integrations</h3>
                        <div class="space-y-8">
                            <div>
                                <h4 class="font-semibold text-white">UTM Link Builder</h4>
                                <p class="text-sm text-gray-400 mb-3">Create trackable links to measure campaign performance effectively.</p>
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    <input type="text" placeholder="Campaign Source (e.g., google)" class="bg-[#111122] border border-gray-700 rounded-md p-2 text-sm">
                                    <input type="text" placeholder="Campaign Medium (e.g., cpc)" class="bg-[#111122] border border-gray-700 rounded-md p-2 text-sm">
                                    <input type="text" placeholder="Campaign Name (e.g., summer_sale)" class="bg-[#111122] border border-gray-700 rounded-md p-2 text-sm">
                                </div>
                            </div>
                             <div>
                                <h4 class="font-semibold text-white">Tracking Pixels</h4>
                                 <p class="text-sm text-gray-400 mb-3">Add tracking pixels to your pages to analyze visitor behavior and optimize ads.</p>
                                <div class="flex flex-col md:flex-row gap-4">
                                    <div class="flex-1 content-card p-4 rounded-lg border border-transparent hover:border-indigo-500 cursor-pointer flex justify-between items-center">
                                        <div>
                                            <p class="font-bold">Meta Pixel</p>
                                            <p class="text-xs text-gray-400">Track Facebook & Instagram ad performance.</p>
                                        </div>
                                        <button class="text-sm font-semibold bg-indigo-600 hover:bg-indigo-500 px-3 py-1 rounded-md">Connect</button>
                                    </div>
                                    <div class="flex-1 content-card p-4 rounded-lg border border-transparent hover:border-indigo-500 cursor-pointer flex justify-between items-center">
                                        <div>
                                            <p class="font-bold">Google Analytics</p>
                                            <p class="text-xs text-gray-400">Get in-depth insights into your traffic.</p>
                                        </div>
                                         <button class="text-sm font-semibold bg-indigo-600 hover:bg-indigo-500 px-3 py-1 rounded-md">Connect</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <div id="hosting-modal" class="modal fixed inset-0 z-50 flex items-center justify-center p-4"><div class="modal-backdrop fixed inset-0"></div><div class="content-card rounded-xl w-full max-w-4xl max-h-[90vh] flex flex-col relative"><div class="p-6 border-b border-gray-700 flex justify-between items-center"><h3 class="text-xl font-bold">Host a New Page</h3><button id="close-modal-btn" class="p-2 rounded-full hover:bg-gray-700 -mr-2"><i data-lucide="x" class="w-5 h-5"></i></button></div><div class="p-6 overflow-y-auto flex-1"><div class="grid grid-cols-1 gap-6"><div><label for="page-title-input" class="block text-sm font-medium mb-2 text-gray-400">Page Title</label><input type="text" id="page-title-input" class="w-full bg-[#111122] border border-gray-700 rounded-md p-2 focus:ring-2 focus:ring-indigo-500 focus:outline-none" placeholder="e.g., Summer Sale Landing Page"></div><div><label for="full-code" class="block text-sm font-medium mb-2 text-gray-400">HTML, CSS & JS Code</label><textarea id="full-code" class="code-editor w-full p-4 rounded-md h-64" placeholder="<!-- Your entire page code goes here -->"></textarea></div><div><h2 class="text-md font-bold mb-3 text-gray-300">Dashboard Tracking (Optional)</h2><p class="text-xs text-gray-500 mb-4">Provide CSS selectors for elements on your page you want to track.</p><div class="grid grid-cols-1 md:grid-cols-2 gap-4"><div><label for="track-conversion-btn" class="block text-xs font-medium mb-1 text-gray-400">Conversion Button</label><input type="text" id="track-conversion-btn" class="w-full bg-[#1a1a2e] border border-gray-600 rounded-md p-2 text-sm" placeholder="#confirm-order, .buy-btn"></div><div><label for="track-product-name" class="block text-xs font-medium mb-1 text-gray-400">Product Name</label><input type="text" id="track-product-name" class="w-full bg-[#1a1a2e] border border-gray-600 rounded-md p-2 text-sm" placeholder=".product-title, #p-name"></div><div><label for="track-product-price" class="block text-xs font-medium mb-1 text-gray-400">Product Price</label><input type="text" id="track-product-price" class="w-full bg-[#1a1a2e] border border-gray-600 rounded-md p-2 text-sm" placeholder="#price, .amount"></div><div><label for="track-customer-name" class="block text-xs font-medium mb-1 text-gray-400">Customer Name Input</label><input type="text" id="track-customer-name" class="w-full bg-[#1a1a2e] border border-gray-600 rounded-md p-2 text-sm" placeholder="input[name='customer_name']"></div><div><label for="track-customer-phone" class="block text-xs font-medium mb-1 text-gray-400">Customer Phone Input</label><input type="text" id="track-customer-phone" class="w-full bg-[#1a1a2e] border border-gray-600 rounded-md p-2 text-sm" placeholder="#phone, input[name='phone']"></div><div><label for="track-customer-city" class="block text-xs font-medium mb-1 text-gray-400">Customer City Input</label><input type="text" id="track-customer-city" class="w-full bg-[#1a1a2e] border border-gray-600 rounded-md p-2 text-sm" placeholder="#city, input[name='city']"></div></div></div><div><h2 class="text-md font-bold mb-3 text-gray-300">Choose Hosting Duration</h2><div id="hosting-options-container" class="grid grid-cols-3 gap-4"></div></div></div></div><div class="p-6 border-t border-gray-700 flex justify-between items-center"><div id="modal-error" class="text-red-400 text-sm"></div><div class="flex gap-4"><button id="cancel-deploy-btn" class="py-2 px-5 rounded-lg font-semibold hover:bg-gray-700">Cancel</button><button id="deploy-btn" class="py-2 px-5 rounded-lg font-semibold bg-emerald-600 hover:bg-emerald-500 text-white flex items-center gap-2 disabled:opacity-50"><i data-lucide="rocket" class="w-5 h-5"></i> Deploy Now</button></div></div></div></div>
    <div id="delete-modal" class="modal fixed inset-0 z-50 flex items-center justify-center p-4"><div class="modal-backdrop fixed inset-0"></div><div class="content-card rounded-xl w-full max-w-md relative"><div class="p-6 text-center"><i data-lucide="alert-triangle" class="w-16 h-16 mx-auto text-red-500"></i><h3 class="mt-4 text-xl font-bold">Delete Page?</h3><p class="mt-2 text-sm text-gray-400">Are you sure you want to delete this page? This action is permanent and cannot be undone.</p></div><div class="p-4 bg-gray-700/20 flex justify-end gap-4 rounded-b-xl"><button id="cancel-delete-btn" class="py-2 px-5 rounded-lg font-semibold hover:bg-gray-600">Cancel</button><button id="confirm-delete-btn" class="py-2 px-5 rounded-lg font-semibold bg-red-600 hover:bg-red-500 text-white">Delete</button></div></div></div>

    <script>
        // --- FAKE DATA & STATE ---
        const state = {
            pages: [
                { id: 'ab12cd34', title: 'Futuristic Gadget', status: 'Live', expirationDate: '2025-09-28', code: '<!DOCTYPE html><html><body><h1>Summer Sale!</h1></body></html>', tracking: { productName: 'Futuristic Gadget', productPrice: '$99.99' }, stock: 120 },
                { id: 'ef56gh78', title: 'Cyber Watch', status: 'Live', expirationDate: '2025-09-15', code: '<!DOCTYPE html><html><body><h1>Cyber Watch!</h1></body></html>', tracking: { productName: 'Cyber Watch', productPrice: '$249.50' }, stock: 8 }
            ],
            orders: [
                { id: '#8A34F1', customer: 'Alex Johnson', status: 'Shipped', date: 'Aug 29, 2025', amount: '$129.99' },
                { id: '#2B90E2', customer: 'Maria Garcia', status: 'Processing', date: 'Aug 29, 2025', amount: '$75.50' },
                { id: '#C5D8A9', customer: 'Ken Tanaka', status: 'Delivered', date: 'Aug 28, 2025', amount: '$249.50' },
                { id: '#F1E2D3', customer: 'Aisha Khan', status: 'Cancelled', date: 'Aug 27, 2025', amount: '$49.99' },
                { id: '#9B8C7A', customer: 'Liam Smith', status: 'Shipped', date: 'Aug 26, 2025', amount: '$199.00' }
            ],
            customers: [
                { id: 1, name: 'Alex Johnson', phone: '******-555-0174', location: 'New York, USA', totalSpent: '$1,250' },
                { id: 2, name: 'Maria Garcia', phone: '+34 91-555-0123', location: 'Madrid, Spain', totalSpent: '$875' },
                { id: 3, name: 'Ken Tanaka', phone: '+81 3-5555-0159', location: 'Tokyo, Japan', totalSpent: '$2,110' },
                { id: 4, name: 'Aisha Khan', phone: '+971 4-555-0182', location: 'Dubai, UAE', totalSpent: '$450' },
            ]
        };
        let pageIdToDelete = null;

        // --- CHARTING ---
        const chartInstances = { sales: null, traffic: null, country: null, device: null };
        const commonChartOptions = {
            responsive: true, maintainAspectRatio: false,
            plugins: { legend: { labels: { color: '#e0e0e0' } } },
            scales: {
                x: { ticks: { color: '#9ca3af' }, grid: { color: 'rgba(255, 255, 255, 0.1)' } },
                y: { ticks: { color: '#9ca3af' }, grid: { color: 'rgba(255, 255, 255, 0.1)' } }
            }
        };

        const renderChart = (ctxId, config) => {
            const ctx = document.getElementById(ctxId)?.getContext('2d');
            if (!ctx) return;
            if (chartInstances[ctxId]) chartInstances[ctxId].destroy();
            chartInstances[ctxId] = new Chart(ctx, config);
        };
        
        const generateAnalyticsData = () => ({
            countryData: { labels: ['USA', 'Germany', 'Japan', 'UK', 'Canada'], data: Array.from({length: 5}, () => Math.floor(Math.random() * 3000) + 500) },
            deviceData: { labels: ['Desktop', 'Mobile', 'Tablet'], data: Array.from({length: 3}, () => Math.floor(Math.random() * 100)) }
        });

        const updateAnalyticsCharts = (pageId) => {
            const page = state.pages.find(p => p.id === pageId);
            const title = page ? page.title : "All Pages";
            getElement("analytics-page-title").textContent = `Showing data for: ${title}`;
            
            const { countryData, deviceData } = generateAnalyticsData(); // Generate new random data for demo
            
            renderChart('country-chart', { type: 'bar', data: { labels: countryData.labels, datasets: [{ label: 'Visitors', data: countryData.data, backgroundColor: '#6366f1' }] }, options: { ...commonChartOptions, indexAxis: 'y' } });
            renderChart('device-chart', { type: 'pie', data: { labels: deviceData.labels, datasets: [{ data: deviceData.data, backgroundColor: ['#818cf8', '#60a5fa', '#34d399'] }] }, options: { ...commonChartOptions, scales: {} } });
        };


        // --- UI ELEMENTS & HELPERS ---
        const getElement = (id) => document.getElementById(id);
        const query = (selector) => document.querySelector(selector);
        const queryAll = (selector) => document.querySelectorAll(selector);

        // --- NAVIGATION LOGIC ---
        const sidebar = getElement('sidebar');
        const sidebarOverlay = getElement('sidebar-overlay');

        const closeSidebar = () => {
            sidebar.classList.remove('open');
            sidebarOverlay.classList.add('hidden');
        };

        const openSidebar = () => {
            sidebar.classList.add('open');
            sidebarOverlay.classList.remove('hidden');
        };

        const navigateToPage = (pageId, contextId = null) => {
            queryAll('.sidebar-link').forEach(l => l.classList.toggle('active', l.dataset.page === pageId));
            queryAll('.page-content').forEach(page => page.classList.toggle('hidden', page.id !== pageId));
            const activeLink = query(`.sidebar-link[data-page="${pageId}"]`);
            getElement('page-title').textContent = activeLink.textContent.trim();
            if (window.innerWidth < 1024) closeSidebar();

            setTimeout(() => {
                if (pageId === 'page-dashboard') {
                    renderChart('sales-chart', { type: 'line', data: { labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'], datasets: [{ label: 'Sales', data: [1200, 1900, 3000, 5000, 2300, 3100, 4200], borderColor: '#6366f1', backgroundColor: 'rgba(99, 102, 241, 0.2)', fill: true, tension: 0.4 }] }, options: commonChartOptions });
                    renderChart('traffic-chart', { type: 'doughnut', data: { labels: ['Google', 'Facebook', 'Direct', 'Other'], datasets: [{ data: [300, 250, 150, 100], backgroundColor: ['#818cf8', '#60a5fa', '#34d399', '#f87171'] }] }, options: { ...commonChartOptions, scales: {} } });
                }
                if (pageId === 'page-analytics') {
                    getElement('analytics-page-selector').value = contextId || 'all';
                    updateAnalyticsCharts(contextId);
                }
            }, 0);
        };
        
        queryAll('.sidebar-link').forEach(link => {
            if (link.dataset.page) {
                 link.addEventListener('click', (e) => { e.preventDefault(); navigateToPage(link.dataset.page); });
            }
        });
        
        const userMenuDropdown = getElement('user-menu-dropdown');
        const notificationDropdown = getElement('notification-dropdown');

        getElement('menu-btn').addEventListener('click', (e) => {
            e.stopPropagation();
            sidebar.classList.contains('open') ? closeSidebar() : openSidebar();
        });
        sidebarOverlay.addEventListener('click', closeSidebar);
        
        getElement('user-menu-btn').addEventListener('click', (e) => { 
            e.stopPropagation(); 
            userMenuDropdown.classList.toggle('hidden');
            notificationDropdown.classList.add('hidden');
        });
        
        getElement('notification-btn').addEventListener('click', (e) => {
            e.stopPropagation();
            notificationDropdown.classList.toggle('hidden');
            userMenuDropdown.classList.add('hidden');
            getElement('notification-dot').classList.add('hidden');
        });
        
        getElement('view-all-notifications-btn').addEventListener('click', (e) => {
             e.preventDefault();
             navigateToPage('page-orders');
             notificationDropdown.classList.add('hidden');
        });


        window.addEventListener('click', () => {
            userMenuDropdown.classList.add('hidden');
            notificationDropdown.classList.add('hidden');
        });


        // --- RENDER FUNCTIONS ---
        const getStatusBadge = (status) => {
            const statusClasses = { 'Shipped': 'bg-emerald-500/20 text-emerald-400', 'Delivered': 'bg-blue-500/20 text-blue-400', 'Processing': 'bg-yellow-500/20 text-yellow-400', 'Cancelled': 'bg-red-500/20 text-red-400', 'Live': 'bg-emerald-500/20 text-emerald-400' };
            return `<span class="${statusClasses[status] || 'bg-gray-500/20 text-gray-400'} text-xs font-medium px-2.5 py-1 rounded-full">${status}</span>`;
        };
        const createTable = (headers, rows) => `<table class="w-full text-sm text-left"><thead class="border-b border-gray-700 text-xs text-gray-400 uppercase"><tr>${headers}</tr></thead><tbody>${rows}</tbody></table>`;
        
        const renderRecentOrders = () => { getElement('recent-orders-container').innerHTML = `<h3 class="font-bold p-5">Recent Orders</h3><div class="overflow-x-auto">${createTable('<th scope="col" class="px-6 py-3">Order ID</th><th scope="col" class="px-6 py-3">Customer</th><th scope="col" class="px-6 py-3">Status</th><th scope="col" class="px-6 py-3 text-right">Amount</th>', state.orders.slice(0, 5).map(order => `<tr class="border-b border-gray-800 hover:bg-gray-700/20"><td class="px-6 py-4 font-mono text-indigo-400">${order.id}</td><td class="px-6 py-4">${order.customer}</td><td class="px-6 py-4">${getStatusBadge(order.status)}</td><td class="px-6 py-4 text-right font-medium">${order.amount}</td></tr>`).join(''))}</div>`; };
        const renderAllOrders = () => { getElement('all-orders-container').innerHTML = `<h3 class="font-bold text-lg mb-4">All Orders</h3><div class="overflow-x-auto">${createTable('<th class="px-6 py-3">Order ID</th><th class="px-6 py-3">Customer</th><th class="px-6 py-3">Status</th><th class="px-6 py-3">Date</th><th class="px-6 py-3 text-right">Amount</th>', state.orders.map(o => `<tr class="border-b border-gray-800 hover:bg-gray-700/20"><td class="px-6 py-4 font-mono text-indigo-400">${o.id}</td><td class="px-6 py-4">${o.customer}</td><td class="px-6 py-4">${getStatusBadge(o.status)}</td><td class="px-6 py-4 text-gray-400">${o.date}</td><td class="px-6 py-4 text-right font-medium">${o.amount}</td></tr>`).join(''))}</div>`; };
        const renderCustomers = () => { getElement('all-customers-container').innerHTML = `<h3 class="font-bold text-lg mb-4">All Customers</h3><div class="overflow-x-auto">${createTable('<th class="px-6 py-3">Name</th><th class="px-6 py-3">Phone Number</th><th class="px-6 py-3">Location</th><th class="px-6 py-3 text-right">Total Spent</th>', state.customers.map(c => `<tr class="border-b border-gray-800 hover:bg-gray-700/20"><td class="px-6 py-4 font-medium">${c.name}</td><td class="px-6 py-4 text-gray-400">${c.phone}</td><td class="px-6 py-4 text-gray-400">${c.location}</td><td class="px-6 py-4 text-right font-medium">${c.totalSpent}</td></tr>`).join(''))}</div>`; };
        
        const renderProducts = () => {
            const container = getElement('products-list-container');
            const productsFromPages = state.pages.filter(p => p.tracking && p.tracking.productName);
            container.innerHTML = productsFromPages.length === 0 ? `<div class="col-span-full text-center py-16"><i data-lucide="package" class="w-16 h-16 mx-auto text-gray-600"></i><h3 class="mt-4 text-lg font-semibold">No Products Found</h3><p class="mt-1 text-sm text-gray-400">Products appear here when you host a page with product tracking.</p></div>` : productsFromPages.map(p => `<div class="content-card rounded-lg overflow-hidden"><img src="https://placehold.co/600x400/0a0a1a/e0e0e0?text=${encodeURIComponent(p.tracking.productName)}" alt="Product Image" class="w-full h-40 object-cover"><div class="p-4"><h4 class="font-bold">${p.tracking.productName}</h4><p class="text-sm text-gray-400 mb-2">${p.tracking.productPrice}</p><div class="flex justify-between items-center text-xs"><span class="${p.stock < 10 ? 'text-red-400' : 'text-emerald-400'}">In Stock: ${p.stock}</span><a href="#" data-page-id="${p.id}" class="view-hosted-page-link text-indigo-400 hover:underline">View Page</a></div></div></div>`).join('');
            lucide.createIcons();
        };

        const renderPagesTable = () => {
            const container = getElement('pages-list-container');
            if (state.pages.length === 0) {
                 container.innerHTML = `<div class="text-center py-16"><i data-lucide="globe-2" class="w-16 h-16 mx-auto text-gray-600"></i><h3 class="mt-4 text-lg font-semibold">No Hosted Pages Yet</h3><p class="mt-1 text-sm text-gray-400">Click "Host New Page" to deploy your first website.</p></div>`;
            } else {
                 const pageRows = state.pages.map(p => {
                    const expDate = new Date(p.expirationDate);
                    const diffDays = Math.ceil((expDate - new Date()) / (1000 * 60 * 60 * 24));
                    const expiresInText = diffDays > 0 ? `${diffDays} Day(s)` : diffDays === 0 ? 'Today' : 'Expired';
                    const formattedDate = expDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
                    return `<tr class="border-b border-gray-800 hover:bg-gray-700/20" data-page-id="${p.id}"><td class="px-6 py-4 font-medium">${p.title}</td><td class="px-6 py-4">${getStatusBadge(p.status)}</td><td class="px-6 py-4 text-gray-400"><div>${formattedDate}</div><div class="text-xs text-gray-500">(${expiresInText})</div></td><td class="px-6 py-4"><div class="flex items-center gap-2"><a href="https://outa.online/p/${p.id}" target="_blank" class="text-indigo-400 hover:underline">outa.online/p/${p.id}</a><button title="Copy Link" class="copy-link-btn p-1 hover:bg-gray-600 rounded-md"><i data-lucide="copy" class="w-3 h-3"></i></button></div></td><td class="px-6 py-4 text-right flex justify-end gap-2"><button title="View Analytics" class="view-analytics-btn p-2 hover:bg-gray-600 rounded-md"><i data-lucide="bar-chart-3" class="w-4 h-4"></i></button><button title="Delete Page" class="delete-page-btn p-2 hover:bg-gray-600 rounded-md"><i data-lucide="trash-2" class="w-4 h-4 text-red-400"></i></button></td></tr>`;
                 }).join('');
                 container.innerHTML = createTable('<th class="px-6 py-3">Page Title</th><th class="px-6 py-3">Status</th><th class="px-6 py-3">Expires</th><th class="px-6 py-3">Link</th><th class="px-6 py-3 text-right">Actions</th>', pageRows);
            }
            lucide.createIcons();
        };

        // --- MODAL LOGIC ---
        const hostingModal = getElement('hosting-modal');
        const deleteModal = getElement('delete-modal');
        const openModal = (modal) => modal.classList.add('open');
        const closeModal = (modal) => {
            modal.classList.remove('open');
            if (modal === hostingModal) {
                 ['page-title-input', 'full-code', 'track-conversion-btn', 'track-product-name', 'track-product-price', 'track-customer-name', 'track-customer-phone', 'track-customer-city'].forEach(id => getElement(id).value = '');
                 getElement('modal-error').textContent = '';
            }
        };
        
        const optionsContainer = getElement('hosting-options-container');
        const DURATION_OPTIONS = [ { days: 1, title: '24 Hours', subtitle: 'Quick Test' }, { days: 7, title: '7 Days', subtitle: 'Preview Link', default: true }, { days: 30, title: '30 Days', subtitle: 'Beta Launch' } ];
        let selectedDuration = DURATION_OPTIONS.find(opt => opt.default).days;

        optionsContainer.innerHTML = DURATION_OPTIONS.map(opt => `<div class="content-card rounded-lg p-3 text-center cursor-pointer border-2 ${opt.default ? 'border-indigo-500' : 'border-transparent'}" data-duration="${opt.days}"><h3 class="font-bold text-md">${opt.title}</h3><p class="text-xs text-gray-400">${opt.subtitle}</p></div>`).join('');
        optionsContainer.addEventListener('click', (e) => {
            const selectedOption = e.target.closest('[data-duration]');
            if (!selectedOption) return;
            optionsContainer.querySelectorAll('[data-duration]').forEach(opt => opt.classList.remove('border-indigo-500'));
            selectedOption.classList.add('border-indigo-500');
            selectedDuration = parseInt(selectedOption.dataset.duration, 10);
        });

        getElement('host-new-page-btn').addEventListener('click', () => openModal(hostingModal));
        getElement('close-modal-btn').addEventListener('click', () => closeModal(hostingModal));
        getElement('cancel-deploy-btn').addEventListener('click', () => closeModal(hostingModal));
        getElement('cancel-delete-btn').addEventListener('click', () => closeModal(deleteModal));
        
        getElement('confirm-delete-btn').addEventListener('click', () => {
            state.pages = state.pages.filter(p => p.id !== pageIdToDelete);
            renderPagesTable(); renderProducts(); closeModal(deleteModal); pageIdToDelete = null;
        });

        // --- DEPLOY & ACTIONS ---
        getElement('deploy-btn').addEventListener('click', () => {
            const now = new Date();
            const expDate = new Date(now.setDate(now.getDate() + selectedDuration));
            const newPage = {
                 id: Math.random().toString(36).substring(2, 10),
                 title: getElement('page-title-input').value || 'Untitled Page', status: 'Live', 
                 expirationDate: expDate.toISOString().split('T')[0],
                 code: getElement('full-code').value,
                 tracking: { productName: getElement('track-product-name').value.trim(), productPrice: getElement('track-product-price').value.trim() },
                 stock: Math.floor(Math.random() * 100) + 10
             };
             state.pages.push(newPage);
             renderPagesTable(); renderProducts(); closeModal(hostingModal);
        });

        getElement('pages-list-container').addEventListener('click', e => {
            const row = e.target.closest('tr');
            if (!row) return;
            const pageId = row.dataset.pageId;
            const copyBtn = e.target.closest('.copy-link-btn');

            if (copyBtn) {
                navigator.clipboard.writeText(copyBtn.previousElementSibling.href).then(() => {
                    const originalIcon = copyBtn.innerHTML;
                    copyBtn.innerHTML = `<i data-lucide="check" class="w-3 h-3 text-emerald-400"></i>`;
                    lucide.createIcons();
                    setTimeout(() => { copyBtn.innerHTML = originalIcon; lucide.createIcons(); }, 2000);
                });
            } else if (e.target.closest('.delete-page-btn')) {
                pageIdToDelete = pageId; openModal(deleteModal);
            } else if (e.target.closest('.view-analytics-btn')) {
                navigateToPage('page-analytics', pageId);
            }
        });
        
        getElement('products-list-container').addEventListener('click', e => {
             const link = e.target.closest('.view-hosted-page-link');
             if(link) { e.preventDefault(); navigateToPage('page-hosted-pages'); }
        });
        
        getElement('analytics-page-selector').addEventListener('change', (e) => {
            updateAnalyticsCharts(e.target.value === 'all' ? null : e.target.value);
        });

        // --- DATE & TIME ---
        const updateTime = () => {
            const now = new Date();
            getElement('current-time').textContent = now.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' });
            getElement('current-date').textContent = now.toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' });
        };

        const populateAnalyticsSelector = () => {
            const selector = getElement('analytics-page-selector');
            selector.innerHTML = '<option value="all">All Pages</option>';
            state.pages.forEach(p => selector.innerHTML += `<option value="${p.id}">${p.title}</option>`);
        };

        // --- INITIAL LOAD ---
        document.addEventListener('DOMContentLoaded', () => {
            lucide.createIcons();
            updateTime();
            setInterval(updateTime, 1000 * 60);
            renderPagesTable(); renderRecentOrders(); renderAllOrders();
            renderCustomers(); renderProducts(); populateAnalyticsSelector();
            navigateToPage('page-dashboard');
        });
    </script>
</body>
</html>

